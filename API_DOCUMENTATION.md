# Voice Assistant API Documentation

## Overview

The Voice Assistant API provides a REST interface and WebSocket gateway for the voice streaming chatbot system. This allows the voice assistant to run on any machine with proper API access, enabling remote deployment and management.

## Base URL

```
http://localhost:8000  # Local development
https://your-domain.com/api  # Production with reverse proxy
```

## Authentication

All API endpoints (except `/health`) require authentication using Bearer tokens (API keys).

### Headers
```
Authorization: Bearer YOUR_API_KEY
```

### Getting an API Key

1. **Default Key**: A default API key is generated on first startup (check server logs)
2. **Create New Key**: Use the `/security/api-key` endpoint (requires admin permissions)

## API Endpoints

### Health & Status

#### GET /health
Health check endpoint (no authentication required).

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********.0,
  "uptime": 3600.0,
  "version": "1.0.0",
  "system_info": {
    "platform": "posix",
    "python_version": "3.11.0",
    "hostname": "voice-server"
  }
}
```

#### GET /status
Get detailed service status.

**Response:**
```json
{
  "status": "running",
  "websocket_server_running": true,
  "active_connections": 2,
  "uptime": 3600.0,
  "memory_usage": {
    "rss": 256.5,
    "vms": 512.0,
    "percent": 2.5
  },
  "cpu_usage": 15.2
}
```

#### GET /metrics
Get detailed system metrics.

**Response:**
```json
{
  "cpu_percent": 15.2,
  "memory_percent": 45.8,
  "memory_available": 8.5,
  "memory_total": 16.0,
  "disk_usage": {
    "total": 500.0,
    "used": 250.0,
    "free": 250.0,
    "percent": 50.0
  },
  "network_connections": 25,
  "timestamp": **********.0
}
```

### Configuration Management

#### GET /config
Get complete configuration.

#### GET /config/schema
Get configuration schema for validation.

#### GET /config/{section}
Get specific configuration section.

#### GET /config/{section}/{key}
Get specific configuration value.

#### POST /config
Update configuration value.

**Request Body:**
```json
{
  "section": "rag",
  "key": "model_name",
  "value": "gpt-4o"
}
```

#### POST /config/import
Import configuration from JSON.

#### GET /config/export
Export current configuration as JSON.

#### DELETE /config
Reset configuration to defaults.

**Query Parameters:**
- `section` (optional): Reset specific section
- `key` (optional): Reset specific key

### Service Control

#### POST /service/start
Start the voice service.

#### POST /service/stop
Stop the voice service.

#### POST /service/restart
Restart the voice service.

### Security Management

#### GET /security/audit
Get security audit logs (admin only).

**Query Parameters:**
- `limit` (optional): Number of events to return (default: 100)

#### POST /security/api-key
Create a new API key (admin only).

**Query Parameters:**
- `name`: Name for the API key
- `permissions` (optional): List of permissions (default: ["read", "write"])

#### DELETE /security/api-key/{api_key}
Revoke an API key (admin only).

#### GET /security/api-keys
List all API keys (admin only).

### WebSocket Connection

#### WS /ws/voice
WebSocket proxy to the voice streaming server.

**Optional Query Parameter:**
- `token`: API key for authentication

**Message Format:**
```json
{
  "type": "audio_chunk",
  "audio_data": "base64_encoded_audio"
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Invalid or inactive API key"
}
```

### 403 Forbidden
```json
{
  "detail": "Insufficient permissions"
}
```

### 429 Too Many Requests
```json
{
  "detail": "Rate limit exceeded"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Error message describing the issue"
}
```

## Rate Limiting

- **API Endpoints**: 100 requests per hour per API key
- **WebSocket**: 5 connections per minute per IP

## Permissions

### read
- Access to GET endpoints
- View configuration and status

### write
- Access to POST, PUT, DELETE endpoints
- Modify configuration and control services

### admin
- All permissions
- Manage API keys
- View audit logs

## Configuration Sections

### rag
- `model_name`: LLM model name
- `forms_path`: Path to forms JSON file
- `file_path`: Path to PDF file
- `index_path`: Path to RAG index

### audio
- `rate`: Audio sample rate (integer)
- `pause_threshold`: Pause detection threshold (float)

### streaming
- `silence_threshold`: Silence detection threshold (float)
- `max_silence_duration`: Maximum silence duration (float)
- `min_audio_duration`: Minimum audio duration (float)

### transcription
- `model`: Transcription model name
- `language`: Transcription language (fixed to "en")
- `temperature`: Transcription temperature (float)
- `prompt`: Transcription context prompt

### tts
- `enable_tts`: Enable text-to-speech (boolean)
- `autoplay_audio`: Enable audio autoplay (boolean)

### ui
- `page_title`: UI page title
- `layout`: UI layout ("wide" or "centered")

### application
- `default_application`: Default application name
- `default_form`: Default form name

### agent
- `verbose`: Enable verbose logging (boolean)

### session
- `enable_reset`: Enable session reset (boolean)

## Environment Variables

### API Server Configuration
- `VOICE_API_API_HOST`: API server host (default: "0.0.0.0")
- `VOICE_API_API_PORT`: API server port (default: 8000)
- `VOICE_API_WEBSOCKET_HOST`: WebSocket server host (default: "localhost")
- `VOICE_API_WEBSOCKET_PORT`: WebSocket server port (default: 8765)
- `VOICE_API_API_KEY`: Default API key
- `VOICE_API_CORS_ORIGINS`: CORS allowed origins (JSON array)

### Application Configuration
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `PYTHONPATH`: Python path (should include app directory)

## Docker Deployment

### Using Docker Compose
```bash
# Set environment variables
export API_KEY=your-secure-api-key-here
export LOG_LEVEL=INFO

# Start the service
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f voice-assistant-api
```

### Using Docker directly
```bash
docker build -t voice-assistant-api .

docker run -d \
  --name voice-assistant \
  -p 8000:8000 \
  -p 8765:8765 \
  -e VOICE_API_API_KEY=your-secure-api-key \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/rag_index:/app/rag_index \
  voice-assistant-api
```

## Client Examples

### Python Client
```python
import requests
import websocket
import json

# API client
class VoiceAssistantClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {api_key}"}
    
    def get_status(self):
        response = requests.get(f"{self.base_url}/status", headers=self.headers)
        return response.json()
    
    def update_config(self, section, key, value):
        data = {"section": section, "key": key, "value": value}
        response = requests.post(f"{self.base_url}/config", json=data, headers=self.headers)
        return response.json()

# WebSocket client
def on_message(ws, message):
    data = json.loads(message)
    print(f"Received: {data}")

def on_error(ws, error):
    print(f"Error: {error}")

# Connect to WebSocket
ws = websocket.WebSocketApp(
    "ws://localhost:8000/ws/voice?token=your-api-key",
    on_message=on_message,
    on_error=on_error
)
ws.run_forever()
```

### JavaScript Client
```javascript
// API client
class VoiceAssistantAPI {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getStatus() {
        const response = await fetch(`${this.baseUrl}/status`, {
            headers: this.headers
        });
        return response.json();
    }
    
    async updateConfig(section, key, value) {
        const response = await fetch(`${this.baseUrl}/config`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({section, key, value})
        });
        return response.json();
    }
}

// WebSocket client
const ws = new WebSocket('ws://localhost:8000/ws/voice?token=your-api-key');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};

ws.onerror = function(error) {
    console.error('WebSocket error:', error);
};
```

## Security Best Practices

1. **Change Default API Key**: Always change the default API key in production
2. **Use HTTPS**: Deploy with SSL/TLS in production
3. **Restrict CORS**: Configure CORS origins appropriately
4. **Monitor Audit Logs**: Regularly check security audit logs
5. **Rotate API Keys**: Periodically rotate API keys
6. **Network Security**: Use firewalls and VPNs for additional security
7. **Resource Limits**: Configure appropriate resource limits in Docker

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Check API key format and validity
2. **Connection Refused**: Verify server is running and ports are accessible
3. **WebSocket Failures**: Check network connectivity and authentication
4. **High Memory Usage**: Monitor system metrics and adjust resource limits
5. **Configuration Errors**: Validate configuration values against schema

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python api_server.py
```

### Health Checks

Monitor the `/health` endpoint for service availability:
```bash
curl -f http://localhost:8000/health
```
