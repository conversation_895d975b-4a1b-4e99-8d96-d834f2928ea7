# Voice Assistant API

A REST API and WebSocket gateway for the voice streaming chatbot system, enabling deployment on any machine with proper API access.

## 🚀 Quick Start

### Option 1: Using the Startup Script (Recommended)
```bash
# Install dependencies
pip install -r requirements.txt

# Start the API server
python start_api_server.py

# Or with custom settings
python start_api_server.py --host 0.0.0.0 --port 8080 --api-key your-secure-key
```

### Option 2: Using Docker
```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build and run manually
docker build -t voice-assistant-api .
docker run -d -p 8000:8000 -p 8765:8765 voice-assistant-api
```

### Option 3: Direct Python
```bash
# Set environment variables
export VOICE_API_API_KEY=your-secure-api-key
export PYTHONPATH=.

# Run the server
python api_server.py
```

## 📋 Prerequisites

- Python 3.11+
- Required packages (see `requirements.txt`)
- Audio system support (for voice processing)
- Network access for API clients

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   API Server    │    │ Voice Processor │
│                 │    │                 │    │                 │
│ • Streamlit UI  │◄──►│ • REST API      │◄──►│ • WebSocket     │
│ • Web Apps      │    │ • Authentication│    │ • Audio Stream  │
│ • Mobile Apps   │    │ • Configuration │    │ • Transcription │
│ • Python SDK    │    │ • Monitoring    │    │ • TTS           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Configuration

### Environment Variables

```bash
# API Server
VOICE_API_API_HOST=0.0.0.0          # API server host
VOICE_API_API_PORT=8000             # API server port
VOICE_API_WEBSOCKET_HOST=localhost  # WebSocket server host
VOICE_API_WEBSOCKET_PORT=8765       # WebSocket server port
VOICE_API_API_KEY=your-api-key      # Default API key
VOICE_API_CORS_ORIGINS=["*"]        # CORS allowed origins

# Application
PYTHONPATH=.                        # Python path
LOG_LEVEL=INFO                      # Logging level
```

### Configuration Files

The API server uses the same configuration system as the original application:

- `config/default.ini` - Default configuration
- `config/local.ini` - Local overrides
- `config/runtime.ini` - Runtime updates (auto-generated)

## 🔐 Authentication

All API endpoints (except `/health`) require authentication using Bearer tokens:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" http://localhost:8000/status
```

### API Key Management

```python
from api_client import VoiceAssistantClient

client = VoiceAssistantClient("http://localhost:8000", "admin-api-key")

# Create new API key
result = client.create_api_key("client-app", ["read", "write"])
print(f"New API key: {result['api_key']}")

# List API keys
keys = client.list_api_keys()
for key in keys:
    print(f"{key['name']}: {key['key_preview']}")

# Revoke API key
client.revoke_api_key("api-key-to-revoke")
```

## 📡 API Endpoints

### Health & Status
- `GET /health` - Health check (no auth required)
- `GET /status` - Service status
- `GET /metrics` - System metrics

### Configuration
- `GET /config` - Get complete configuration
- `GET /config/{section}` - Get configuration section
- `POST /config` - Update configuration value
- `GET /config/export` - Export configuration
- `POST /config/import` - Import configuration
- `DELETE /config` - Reset configuration

### Service Control
- `POST /service/start` - Start voice service
- `POST /service/stop` - Stop voice service
- `POST /service/restart` - Restart voice service

### Security (Admin only)
- `GET /security/audit` - Get audit logs
- `POST /security/api-key` - Create API key
- `DELETE /security/api-key/{key}` - Revoke API key
- `GET /security/api-keys` - List API keys

### WebSocket
- `WS /ws/voice` - Voice streaming connection

## 🐍 Python Client SDK

```python
from api_client import VoiceAssistantClient, VoiceWebSocketClient

# REST API client
client = VoiceAssistantClient("http://localhost:8000", "your-api-key")

# Check health
health = client.health_check()
print(f"Status: {health['status']}")

# Get configuration
config = client.get_config()
print(f"Model: {config['rag']['model_name']}")

# Update configuration
client.update_config("rag", "model_name", "gpt-4o")

# WebSocket client for voice streaming
ws_client = VoiceWebSocketClient("ws://localhost:8000/ws/voice", "your-api-key")

def on_response(data):
    print(f"AI: {data.get('ai_response', '')}")

ws_client.on_message("transcription_complete", on_response)
ws_client.connect()
ws_client.start_recording()
```

## 🌐 Web Interface

### API-Compatible Streamlit App
```bash
# Start the API-compatible Streamlit interface
streamlit run run-streamlit-voice-api.py --server.port 8503
```

Features:
- Connect to remote API servers
- Voice streaming through API
- Configuration management
- System monitoring
- Security controls

### JavaScript Client
```javascript
// REST API
const api = new VoiceAssistantAPI('http://localhost:8000', 'your-api-key');
const status = await api.getStatus();

// WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/voice?token=your-api-key');
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('Response:', data);
};
```

## 🐳 Docker Deployment

### Docker Compose (Recommended)
```yaml
# docker-compose.yml
version: '3.8'
services:
  voice-assistant-api:
    build: .
    ports:
      - "8000:8000"
      - "8765:8765"
    environment:
      - VOICE_API_API_KEY=your-secure-api-key
    volumes:
      - ./config:/app/config
      - ./rag_index:/app/rag_index
```

```bash
# Deploy
docker-compose up -d

# Scale (if needed)
docker-compose up -d --scale voice-assistant-api=3

# Monitor
docker-compose logs -f
```

### Production with Nginx
```bash
# Start with production profile (includes Nginx)
docker-compose --profile production up -d
```

## 🔒 Security Features

### Authentication & Authorization
- API key-based authentication
- Role-based permissions (read, write, admin)
- Rate limiting (100 requests/hour per key)
- Audit logging

### Security Best Practices
1. Change default API keys
2. Use HTTPS in production
3. Configure CORS appropriately
4. Monitor audit logs
5. Rotate API keys regularly
6. Use network security (VPN, firewall)

### Rate Limiting
- API endpoints: 100 requests/hour per API key
- WebSocket connections: 5 connections/minute per IP

## 📊 Monitoring

### Health Checks
```bash
# Basic health check
curl http://localhost:8000/health

# Detailed status
curl -H "Authorization: Bearer API_KEY" http://localhost:8000/status

# System metrics
curl -H "Authorization: Bearer API_KEY" http://localhost:8000/metrics
```

### Metrics Available
- CPU usage
- Memory usage
- Disk usage
- Network connections
- Active WebSocket connections
- Service uptime

## 🔧 Development

### Local Development
```bash
# Install dependencies
pip install -r requirements.txt

# Start in development mode
python start_api_server.py --reload --log-level debug

# Run tests (if available)
python -m pytest tests/
```

### API Documentation
- Interactive docs: `http://localhost:8000/docs`
- OpenAPI spec: `http://localhost:8000/openapi.json`
- ReDoc: `http://localhost:8000/redoc`

## 🚨 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if server is running
   curl http://localhost:8000/health
   
   # Check logs
   docker-compose logs voice-assistant-api
   ```

2. **Authentication Errors**
   ```bash
   # Check API key
   curl -H "Authorization: Bearer YOUR_KEY" http://localhost:8000/status
   
   # Generate new key (if admin)
   python -c "from security import api_key_manager; print(api_key_manager.generate_api_key('test'))"
   ```

3. **WebSocket Connection Issues**
   - Verify WebSocket URL format
   - Check authentication token
   - Ensure firewall allows WebSocket connections

4. **High Resource Usage**
   - Monitor `/metrics` endpoint
   - Adjust Docker resource limits
   - Check for memory leaks in logs

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python api_server.py

# Or with startup script
python start_api_server.py --log-level debug
```

## 📚 Additional Resources

- [API Documentation](API_DOCUMENTATION.md) - Complete API reference
- [Original Voice Assistant](STREAMING_VOICE_README.md) - Local setup guide
- [Configuration Guide](VOICE_ASSISTANT_CONFIG.md) - Configuration options
- [Docker Hub](https://hub.docker.com) - Pre-built images (if published)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test
4. Submit a pull request

## 📄 License

[Your License Here]

## 🆘 Support

- GitHub Issues: [Repository Issues](https://github.com/your-repo/issues)
- Documentation: [API_DOCUMENTATION.md](API_DOCUMENTATION.md)
- Email: <EMAIL>
