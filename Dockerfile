# Voice Assistant API Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    portaudio19-dev \
    python3-dev \
    ffmpeg \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt requirements-api.txt ./

# Install Python dependencies with fallback
RUN pip install --no-cache-dir -r requirements-api.txt || \
    (echo "Installing minimal dependencies..." && \
     pip install fastapi uvicorn websockets psutil requests pydantic pydantic-settings)

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p config rag_index logs

# Set environment variables
ENV PYTHONPATH=/app
ENV VOICE_API_API_HOST=0.0.0.0
ENV VOICE_API_API_PORT=8000
ENV VOICE_API_WEBSOCKET_HOST=localhost
ENV VOICE_API_WEBSOCKET_PORT=8765
ENV VOICE_API_API_KEY=change-this-api-key
ENV LOG_LEVEL=INFO

# Expose ports
EXPOSE 8000 8765

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Create non-root user
RUN useradd -m -u 1000 voiceapi && chown -R voiceapi:voiceapi /app
USER voiceapi

# Start command
CMD ["python", "api_server.py"]
