# Simple Voice Assistant API Dockerfile
# Minimal version with basic dependencies only

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies (minimal)
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install minimal Python dependencies
RUN pip install --no-cache-dir \
    fastapi==0.115.6 \
    uvicorn==0.32.1 \
    websockets==12.0 \
    pydantic==2.10.6 \
    pydantic-settings==2.7.1

# Copy application code
COPY simple_api_server.py .
COPY start_simple_api.py .

# Set environment variables
ENV PYTHONPATH=/app
ENV VOICE_API_API_HOST=0.0.0.0
ENV VOICE_API_API_PORT=8000
ENV VOICE_API_WEBSOCKET_HOST=localhost
ENV VOICE_API_WEBSOCKET_PORT=8765
ENV LOG_LEVEL=INFO

# Expose ports
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Create non-root user
RUN useradd -m -u 1000 voiceapi && chown -R voiceapi:voiceapi /app
USER voiceapi

# Start command
CMD ["python", "simple_api_server.py"]
