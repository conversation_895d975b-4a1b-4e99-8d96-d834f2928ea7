# Windows Setup Guide

This guide helps you set up the Voice Assistant API on Windows, with solutions for common build issues.

## 🚨 Quick Fix for Build Errors

If you're getting the "Microsoft Visual C++ 14.0 or greater is required" error, here are your options:

### Option 1: API-Only Setup (Fastest) ⚡

Skip the problematic packages and run API-only mode:

```bash
# Install API-only dependencies
pip install -r requirements-api.txt

# Start API server
python start_api_only.py
```

This gives you:
- ✅ Full REST API functionality
- ✅ WebSocket connections (basic)
- ✅ Configuration management
- ✅ System monitoring
- ❌ Full audio processing (requires additional setup)

### Option 2: Install Visual C++ Build Tools 🔧

For full functionality including audio processing:

1. **Download Build Tools:**
   - Go to: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Download "Build Tools for Visual Studio 2022"

2. **Install with C++ workload:**
   - Run the installer
   - Select "C++ build tools" workload
   - Install (this may take 15-30 minutes)

3. **Restart your command prompt and try again:**
   ```bash
   pip install -r requirements.txt
   python start_api_server.py
   ```

### Option 3: Use Conda 🐍

Conda handles C++ dependencies better on Windows:

```bash
# Install Miniconda from: https://docs.conda.io/en/latest/miniconda.html

# Create environment
conda create -n voice-api python=3.11
conda activate voice-api

# Install packages
conda install -c conda-forge fastapi uvicorn websockets psutil streamlit
pip install langchain langchain-openai faiss-cpu

# Start server
python start_api_server.py
```

### Option 4: Use Docker 🐳

Bypass Windows build issues entirely:

```bash
# Install Docker Desktop from: https://www.docker.com/products/docker-desktop/

# Build and run
docker-compose up -d

# Check status
curl http://localhost:8000/health
```

## 🎯 Recommended Approach

**For Development/Testing:**
```bash
# Quick start with API-only
pip install -r requirements-api.txt
python start_api_only.py --check  # Check dependencies
python start_api_only.py          # Start server
```

**For Production:**
```bash
# Use Docker for consistent environment
docker-compose up -d
```

## 🔍 Troubleshooting

### Check What's Missing
```bash
python start_api_only.py --check
```

### Common Issues

1. **"av" package fails to build**
   - Solution: Use `requirements-api.txt` instead
   - Or install Visual C++ Build Tools

2. **"No module named 'fastapi'"**
   ```bash
   pip install fastapi uvicorn
   ```

3. **"Permission denied" errors**
   - Run command prompt as Administrator
   - Or use `--user` flag: `pip install --user -r requirements-api.txt`

4. **Python version issues**
   - Ensure Python 3.11+ is installed
   - Check with: `python --version`

### Test Your Setup

```bash
# Test API dependencies
python -c "import fastapi, uvicorn, websockets; print('✅ API dependencies OK')"

# Test optional dependencies
python -c "import langchain; print('✅ LangChain OK')" 2>/dev/null || echo "⚠️ LangChain missing"

# Start minimal server
python start_api_only.py
```

## 🌐 Using the API

Once running, you can:

1. **Check health:** http://localhost:8000/health
2. **View docs:** http://localhost:8000/docs
3. **Use Python client:**
   ```python
   from api_client import VoiceAssistantClient
   client = VoiceAssistantClient("http://localhost:8000", "your-api-key")
   print(client.health_check())
   ```

## 📦 Package Explanations

**Core API (always needed):**
- `fastapi` - Web API framework
- `uvicorn` - ASGI server
- `websockets` - WebSocket support
- `psutil` - System monitoring

**Audio Processing (optional, causes build issues):**
- `aiortc` - WebRTC support (needs C++ compiler)
- `streamlit-webrtc` - Streamlit WebRTC components
- `av` - Audio/video processing (needs C++ compiler)

**AI/ML (optional but recommended):**
- `langchain` - LLM framework
- `faiss-cpu` - Vector search
- `tiktoken` - Token counting

## 🚀 Next Steps

1. **Start with API-only mode** to test functionality
2. **Add full audio processing** later if needed
3. **Deploy with Docker** for production
4. **Use the Python client** to build custom applications

## 💡 Tips

- Use `requirements-api.txt` for faster setup
- Docker avoids all Windows build issues
- API-only mode still provides full remote control
- You can add audio processing later when needed
