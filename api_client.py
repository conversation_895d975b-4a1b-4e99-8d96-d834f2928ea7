"""
Voice Assistant API Client

Python client library for interacting with the Voice Assistant API.
"""

import json
import requests
import websocket
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from urllib.parse import urljoin


class VoiceAssistantAPIError(Exception):
    """Custom exception for API errors"""
    pass


class VoiceAssistantClient:
    """Client for Voice Assistant API"""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        """
        Initialize the API client
        
        Args:
            base_url: Base URL of the API server
            api_key: API key for authentication
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """Make HTTP request to API"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise VoiceAssistantAPIError("Authentication failed - check API key")
            elif e.response.status_code == 403:
                raise VoiceAssistantAPIError("Insufficient permissions")
            elif e.response.status_code == 429:
                raise VoiceAssistantAPIError("Rate limit exceeded")
            else:
                try:
                    error_detail = e.response.json().get('detail', str(e))
                except:
                    error_detail = str(e)
                raise VoiceAssistantAPIError(f"API error: {error_detail}")
        except requests.exceptions.RequestException as e:
            raise VoiceAssistantAPIError(f"Connection error: {str(e)}")
    
    # Health and Status
    def health_check(self) -> Dict:
        """Get health status"""
        return self._make_request('GET', '/health')
    
    def get_status(self) -> Dict:
        """Get service status"""
        return self._make_request('GET', '/status')
    
    def get_metrics(self) -> Dict:
        """Get system metrics"""
        return self._make_request('GET', '/metrics')
    
    # Configuration Management
    def get_config(self) -> Dict:
        """Get complete configuration"""
        return self._make_request('GET', '/config')
    
    def get_config_schema(self) -> Dict:
        """Get configuration schema"""
        return self._make_request('GET', '/config/schema')
    
    def get_config_section(self, section: str) -> Dict:
        """Get specific configuration section"""
        return self._make_request('GET', f'/config/{section}')
    
    def get_config_value(self, section: str, key: str) -> Any:
        """Get specific configuration value"""
        result = self._make_request('GET', f'/config/{section}/{key}')
        return result.get('value')
    
    def update_config(self, section: str, key: str, value: str) -> Dict:
        """Update configuration value"""
        data = {"section": section, "key": key, "value": value}
        return self._make_request('POST', '/config', json=data)
    
    def import_config(self, config_json: str) -> Dict:
        """Import configuration from JSON"""
        return self._make_request('POST', '/config/import', json=config_json)
    
    def export_config(self) -> str:
        """Export current configuration as JSON"""
        result = self._make_request('GET', '/config/export')
        return result.get('config', '{}')
    
    def reset_config(self, section: str = None, key: str = None) -> Dict:
        """Reset configuration to defaults"""
        params = {}
        if section:
            params['section'] = section
        if key:
            params['key'] = key
        return self._make_request('DELETE', '/config', params=params)
    
    # Service Control
    def start_service(self) -> Dict:
        """Start the voice service"""
        return self._make_request('POST', '/service/start')
    
    def stop_service(self) -> Dict:
        """Stop the voice service"""
        return self._make_request('POST', '/service/stop')
    
    def restart_service(self) -> Dict:
        """Restart the voice service"""
        return self._make_request('POST', '/service/restart')
    
    # Security Management (Admin only)
    def get_audit_logs(self, limit: int = 100) -> List[Dict]:
        """Get security audit logs"""
        result = self._make_request('GET', '/security/audit', params={'limit': limit})
        return result.get('events', [])
    
    def create_api_key(self, name: str, permissions: List[str] = None) -> Dict:
        """Create a new API key"""
        params = {'name': name}
        if permissions:
            params['permissions'] = permissions
        return self._make_request('POST', '/security/api-key', params=params)
    
    def revoke_api_key(self, api_key: str) -> Dict:
        """Revoke an API key"""
        return self._make_request('DELETE', f'/security/api-key/{api_key}')
    
    def list_api_keys(self) -> List[Dict]:
        """List all API keys"""
        result = self._make_request('GET', '/security/api-keys')
        return result.get('api_keys', [])


class VoiceWebSocketClient:
    """WebSocket client for voice streaming"""
    
    def __init__(self, ws_url: str = "ws://localhost:8000/ws/voice", api_key: str = None):
        """
        Initialize WebSocket client
        
        Args:
            ws_url: WebSocket URL
            api_key: API key for authentication
        """
        self.ws_url = ws_url
        if api_key:
            self.ws_url += f"?token={api_key}"
        
        self.ws = None
        self.connected = False
        self.message_handlers = {}
        self.error_handler = None
        self.connect_handler = None
        self.disconnect_handler = None
    
    def on_message(self, message_type: str, handler: Callable):
        """Register message handler"""
        self.message_handlers[message_type] = handler
    
    def on_error(self, handler: Callable):
        """Register error handler"""
        self.error_handler = handler
    
    def on_connect(self, handler: Callable):
        """Register connect handler"""
        self.connect_handler = handler
    
    def on_disconnect(self, handler: Callable):
        """Register disconnect handler"""
        self.disconnect_handler = handler
    
    def _on_message(self, ws, message):
        """Internal message handler"""
        try:
            data = json.loads(message)
            message_type = data.get('type', 'unknown')
            
            if message_type in self.message_handlers:
                self.message_handlers[message_type](data)
            elif 'default' in self.message_handlers:
                self.message_handlers['default'](data)
        except Exception as e:
            if self.error_handler:
                self.error_handler(f"Message handling error: {e}")
    
    def _on_error(self, ws, error):
        """Internal error handler"""
        if self.error_handler:
            self.error_handler(error)
    
    def _on_open(self, ws):
        """Internal open handler"""
        self.connected = True
        if self.connect_handler:
            self.connect_handler()
    
    def _on_close(self, ws, close_status_code, close_msg):
        """Internal close handler"""
        self.connected = False
        if self.disconnect_handler:
            self.disconnect_handler(close_status_code, close_msg)
    
    def connect(self):
        """Connect to WebSocket"""
        try:
            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_open=self._on_open,
                on_close=self._on_close
            )
            
            # Start WebSocket in a separate thread
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            # Wait for connection
            timeout = 10
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if not self.connected:
                raise VoiceAssistantAPIError("WebSocket connection timeout")
                
        except Exception as e:
            raise VoiceAssistantAPIError(f"WebSocket connection failed: {e}")
    
    def disconnect(self):
        """Disconnect from WebSocket"""
        if self.ws:
            self.ws.close()
            self.connected = False
    
    def send_message(self, message_type: str, data: Dict = None):
        """Send message to WebSocket"""
        if not self.connected:
            raise VoiceAssistantAPIError("WebSocket not connected")
        
        message = {"type": message_type}
        if data:
            message.update(data)
        
        try:
            self.ws.send(json.dumps(message))
        except Exception as e:
            raise VoiceAssistantAPIError(f"Failed to send message: {e}")
    
    def send_audio_chunk(self, audio_data: str):
        """Send audio chunk"""
        self.send_message("audio_chunk", {"audio_data": audio_data})
    
    def start_recording(self):
        """Start recording session"""
        self.send_message("start_recording")
    
    def stop_recording(self):
        """Stop recording session"""
        self.send_message("stop_recording")


# Example usage
if __name__ == "__main__":
    # API client example
    client = VoiceAssistantClient(
        base_url="http://localhost:8000",
        api_key="your-api-key-here"
    )
    
    try:
        # Check health
        health = client.health_check()
        print(f"Health: {health['status']}")
        
        # Get status
        status = client.get_status()
        print(f"Service status: {status['status']}")
        
        # Get configuration
        config = client.get_config()
        print(f"Model: {config['rag']['model_name']}")
        
        # Update configuration
        result = client.update_config("rag", "model_name", "gpt-4o")
        print(f"Config update: {result['success']}")
        
    except VoiceAssistantAPIError as e:
        print(f"API Error: {e}")
    
    # WebSocket client example
    ws_client = VoiceWebSocketClient(
        ws_url="ws://localhost:8000/ws/voice",
        api_key="your-api-key-here"
    )
    
    def on_response(data):
        print(f"AI Response: {data.get('ai_response', '')}")
    
    def on_error(error):
        print(f"WebSocket Error: {error}")
    
    ws_client.on_message("transcription_complete", on_response)
    ws_client.on_error(on_error)
    
    try:
        ws_client.connect()
        print("WebSocket connected")
        
        # Start recording
        ws_client.start_recording()
        
        # Keep connection alive
        time.sleep(10)
        
        ws_client.disconnect()
        
    except VoiceAssistantAPIError as e:
        print(f"WebSocket Error: {e}")
