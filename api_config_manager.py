"""
API Configuration Manager

Enhanced configuration manager that supports runtime configuration updates
for the Voice Assistant API server.
"""

import json
import os
import configparser
from typing import Dict, Any, Optional
from pathlib import Path

from src.config_manager import VoiceAssistantConfig


class APIConfigManager:
    """Enhanced configuration manager with runtime update support"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.default_config_path = self.config_dir / "default.ini"
        self.local_config_path = self.config_dir / "local.ini"
        self.runtime_config_path = self.config_dir / "runtime.ini"
        
        # Load base configuration
        self.base_config = VoiceAssistantConfig()
        
        # Runtime configuration overrides
        self.runtime_overrides = {}
        
        # Load existing runtime config if it exists
        self._load_runtime_config()
    
    def _load_runtime_config(self):
        """Load runtime configuration overrides"""
        if self.runtime_config_path.exists():
            try:
                parser = configparser.ConfigParser()
                parser.read(self.runtime_config_path)
                
                for section_name in parser.sections():
                    if section_name not in self.runtime_overrides:
                        self.runtime_overrides[section_name] = {}
                    
                    for key, value in parser.items(section_name):
                        self.runtime_overrides[section_name][key] = value
                        
            except Exception as e:
                print(f"Warning: Failed to load runtime config: {e}")
    
    def _save_runtime_config(self):
        """Save runtime configuration overrides to file"""
        try:
            parser = configparser.ConfigParser()
            
            for section_name, section_data in self.runtime_overrides.items():
                parser.add_section(section_name)
                for key, value in section_data.items():
                    parser.set(section_name, key, str(value))
            
            with open(self.runtime_config_path, 'w') as f:
                parser.write(f)
                
        except Exception as e:
            print(f"Warning: Failed to save runtime config: {e}")
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get complete configuration as dictionary"""
        config_dict = {
            "rag": {
                "prefix": getattr(self.base_config, 'prefix', ''),
                "model_name": self._get_value("rag", "model_name", self.base_config.model_name),
                "forms_path": self._get_value("rag", "forms_path", self.base_config.forms_path),
                "file_path": self._get_value("rag", "file_path", self.base_config.file_path),
                "index_path": self._get_value("rag", "index_path", self.base_config.index_path)
            },
            "audio": {
                "rate": self._get_value("audio", "rate", self.base_config.audio_rate),
                "pause_threshold": self._get_value("audio", "pause_threshold", self.base_config.pause_threshold)
            },
            "streaming": {
                "silence_threshold": self._get_value("streaming", "silence_threshold", self.base_config.silence_threshold),
                "max_silence_duration": self._get_value("streaming", "max_silence_duration", self.base_config.max_silence_duration),
                "min_audio_duration": self._get_value("streaming", "min_audio_duration", self.base_config.min_audio_duration),
                "feedback_prevention_delay": self._get_value("streaming", "feedback_prevention_delay", getattr(self.base_config, 'feedback_prevention_delay', 3.0))
            },
            "transcription": {
                "model": self._get_value("transcription", "model", self.base_config.transcription_model),
                "language": self._get_value("transcription", "language", self.base_config.transcription_language),
                "temperature": self._get_value("transcription", "temperature", self.base_config.transcription_temperature),
                "prompt": self._get_value("transcription", "prompt", self.base_config.transcription_prompt)
            },
            "ui": {
                "page_title": self._get_value("ui", "page_title", getattr(self.base_config, 'page_title', 'ERA-IGNITE Voice Assistant')),
                "layout": self._get_value("ui", "layout", getattr(self.base_config, 'layout', 'wide'))
            },
            "application": {
                "default_application": self._get_value("application", "default_application", getattr(self.base_config, 'default_application', 'Accounting')),
                "default_form": self._get_value("application", "default_form", getattr(self.base_config, 'default_form', 'PRINT_CASH_RECEIPTS'))
            },
            "agent": {
                "verbose": self._get_value("agent", "verbose", getattr(self.base_config, 'verbose', False))
            },
            "tts": {
                "enable_tts": self._get_value("tts", "enable_tts", self.base_config.enable_tts),
                "autoplay_audio": self._get_value("tts", "autoplay_audio", self.base_config.autoplay_audio)
            },
            "session": {
                "enable_reset": self._get_value("session", "enable_reset", getattr(self.base_config, 'enable_reset', True))
            }
        }
        
        return config_dict
    
    def _get_value(self, section: str, key: str, default: Any) -> Any:
        """Get configuration value with runtime override support"""
        if section in self.runtime_overrides and key in self.runtime_overrides[section]:
            value = self.runtime_overrides[section][key]
            # Try to convert to appropriate type based on default
            if isinstance(default, bool):
                return value.lower() in ('true', '1', 'yes', 'on')
            elif isinstance(default, int):
                try:
                    return int(value)
                except ValueError:
                    return default
            elif isinstance(default, float):
                try:
                    return float(value)
                except ValueError:
                    return default
            else:
                return value
        return default
    
    def update_config(self, section: str, key: str, value: str) -> bool:
        """Update configuration value at runtime"""
        try:
            # Validate section and key
            valid_sections = ["rag", "audio", "streaming", "transcription", "ui", "application", "agent", "tts", "session"]
            if section not in valid_sections:
                raise ValueError(f"Invalid section: {section}")
            
            # Initialize section if it doesn't exist
            if section not in self.runtime_overrides:
                self.runtime_overrides[section] = {}
            
            # Update the value
            self.runtime_overrides[section][key] = value
            
            # Save to file
            self._save_runtime_config()
            
            return True
            
        except Exception as e:
            print(f"Error updating config: {e}")
            return False
    
    def get_config_value(self, section: str, key: str) -> Optional[Any]:
        """Get a specific configuration value"""
        config_dict = self.get_config_dict()
        if section in config_dict and key in config_dict[section]:
            return config_dict[section][key]
        return None
    
    def reset_config(self, section: Optional[str] = None, key: Optional[str] = None) -> bool:
        """Reset configuration to defaults"""
        try:
            if section is None:
                # Reset all runtime overrides
                self.runtime_overrides = {}
            elif key is None:
                # Reset entire section
                if section in self.runtime_overrides:
                    del self.runtime_overrides[section]
            else:
                # Reset specific key
                if section in self.runtime_overrides and key in self.runtime_overrides[section]:
                    del self.runtime_overrides[section][key]
                    # Remove section if empty
                    if not self.runtime_overrides[section]:
                        del self.runtime_overrides[section]
            
            # Save changes
            self._save_runtime_config()
            return True
            
        except Exception as e:
            print(f"Error resetting config: {e}")
            return False
    
    def export_config(self) -> str:
        """Export current configuration as JSON"""
        return json.dumps(self.get_config_dict(), indent=2)
    
    def import_config(self, config_json: str) -> bool:
        """Import configuration from JSON"""
        try:
            config_data = json.loads(config_json)
            
            # Update runtime overrides
            for section, section_data in config_data.items():
                if section not in self.runtime_overrides:
                    self.runtime_overrides[section] = {}
                
                for key, value in section_data.items():
                    self.runtime_overrides[section][key] = str(value)
            
            # Save changes
            self._save_runtime_config()
            return True
            
        except Exception as e:
            print(f"Error importing config: {e}")
            return False
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Get configuration schema for validation"""
        return {
            "rag": {
                "prefix": {"type": "string", "description": "RAG query prefix"},
                "model_name": {"type": "string", "description": "LLM model name"},
                "forms_path": {"type": "string", "description": "Path to forms JSON file"},
                "file_path": {"type": "string", "description": "Path to PDF file"},
                "index_path": {"type": "string", "description": "Path to RAG index"}
            },
            "audio": {
                "rate": {"type": "integer", "description": "Audio sample rate"},
                "pause_threshold": {"type": "float", "description": "Pause detection threshold"}
            },
            "streaming": {
                "silence_threshold": {"type": "float", "description": "Silence detection threshold"},
                "max_silence_duration": {"type": "float", "description": "Maximum silence duration"},
                "min_audio_duration": {"type": "float", "description": "Minimum audio duration"},
                "feedback_prevention_delay": {"type": "float", "description": "Feedback prevention delay"}
            },
            "transcription": {
                "model": {"type": "string", "description": "Transcription model"},
                "language": {"type": "string", "description": "Transcription language"},
                "temperature": {"type": "float", "description": "Transcription temperature"},
                "prompt": {"type": "string", "description": "Transcription prompt"}
            },
            "ui": {
                "page_title": {"type": "string", "description": "UI page title"},
                "layout": {"type": "string", "description": "UI layout"}
            },
            "application": {
                "default_application": {"type": "string", "description": "Default application"},
                "default_form": {"type": "string", "description": "Default form"}
            },
            "agent": {
                "verbose": {"type": "boolean", "description": "Enable verbose logging"}
            },
            "tts": {
                "enable_tts": {"type": "boolean", "description": "Enable text-to-speech"},
                "autoplay_audio": {"type": "boolean", "description": "Enable audio autoplay"}
            },
            "session": {
                "enable_reset": {"type": "boolean", "description": "Enable session reset"}
            }
        }
