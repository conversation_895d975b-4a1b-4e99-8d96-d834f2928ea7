"""
Voice Assistant API Server

This FastAPI server provides REST endpoints and WebSocket proxy for the voice streaming chatbot,
allowing the system to run from any machine with proper API access.
"""

import asyncio
import json
import logging
import os
import threading
import time
import psutil
from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Any
from datetime import datetime

import uvicorn
import websockets
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings

from src.config_manager import VoiceAssistantConfig
from websocket_voice_server import VoiceStreamProcessor, start_voice_server
from api_config_manager import APIConfigManager
from security import security_middleware, request_validator, audit_logger, api_key_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

class APISettings(BaseSettings):
    """API Server Settings"""
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    websocket_host: str = "localhost"
    websocket_port: int = 8765
    api_key: str = "your-api-key-here"
    cors_origins: List[str] = ["*"]
    
    class Config:
        env_prefix = "VOICE_API_"

# Global settings
settings = APISettings()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.websocket_server_running = False
        self.websocket_server_task = None

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def start_websocket_server(self):
        """Start the internal WebSocket server"""
        if not self.websocket_server_running:
            self.websocket_server_running = True
            try:
                # Start WebSocket server in background thread
                def run_websocket_server():
                    start_voice_server(settings.websocket_host, settings.websocket_port)
                
                websocket_thread = threading.Thread(target=run_websocket_server, daemon=True)
                websocket_thread.start()
                
                # Wait for server to start
                await asyncio.sleep(2)
                logger.info("Internal WebSocket server started")
            except Exception as e:
                logger.error(f"Failed to start WebSocket server: {e}")
                self.websocket_server_running = False
                raise

    async def stop_websocket_server(self):
        """Stop the internal WebSocket server"""
        self.websocket_server_running = False
        logger.info("Internal WebSocket server stopped")

manager = ConnectionManager()

# Global configuration manager
config_manager = APIConfigManager()

# Application start time for uptime calculation
app_start_time = time.time()

# Pydantic models
class ConfigUpdate(BaseModel):
    """Configuration update model"""
    section: str = Field(..., description="Configuration section name")
    key: str = Field(..., description="Configuration key")
    value: str = Field(..., description="Configuration value")

class ServiceStatus(BaseModel):
    """Service status model"""
    status: str
    websocket_server_running: bool
    active_connections: int
    uptime: Optional[float] = None
    memory_usage: Optional[Dict[str, float]] = None
    cpu_usage: Optional[float] = None

class HealthCheck(BaseModel):
    """Health check response model"""
    status: str
    timestamp: float
    version: str = "1.0.0"
    uptime: Optional[float] = None
    system_info: Optional[Dict[str, Any]] = None

class SystemMetrics(BaseModel):
    """System metrics model"""
    cpu_percent: float
    memory_percent: float
    memory_available: float
    memory_total: float
    disk_usage: Dict[str, float]
    network_connections: int
    timestamp: float

# Authentication
async def verify_api_key(key_data: dict = Depends(security_middleware.verify_api_key)):
    """Verify API key authentication"""
    audit_logger.log_event("api_access", key_data.get("name", "unknown"))
    return key_data

# Lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    logger.info("Starting Voice Assistant API Server...")
    await manager.start_websocket_server()
    
    yield
    
    # Shutdown
    logger.info("Shutting down Voice Assistant API Server...")
    await manager.stop_websocket_server()

# FastAPI app
app = FastAPI(
    title="Voice Assistant API",
    description="REST API for Voice Streaming Chatbot",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health endpoints
@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    try:
        uptime = time.time() - app_start_time
        system_info = {
            "platform": os.name,
            "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            "hostname": os.uname().nodename if hasattr(os, 'uname') else "unknown"
        }

        return HealthCheck(
            status="healthy",
            timestamp=time.time(),
            uptime=uptime,
            system_info=system_info
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheck(
            status="unhealthy",
            timestamp=time.time()
        )

@app.get("/status", response_model=ServiceStatus)
async def get_status(key_data: dict = Depends(verify_api_key)):
    """Get service status"""
    try:
        uptime = time.time() - app_start_time

        # Get memory usage
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_usage = {
            "rss": memory_info.rss / 1024 / 1024,  # MB
            "vms": memory_info.vms / 1024 / 1024,  # MB
            "percent": process.memory_percent()
        }

        # Get CPU usage
        cpu_usage = process.cpu_percent()

        return ServiceStatus(
            status="running" if manager.websocket_server_running else "stopped",
            websocket_server_running=manager.websocket_server_running,
            active_connections=len(manager.active_connections),
            uptime=uptime,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage
        )
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return ServiceStatus(
            status="error",
            websocket_server_running=False,
            active_connections=0
        )

@app.get("/metrics", response_model=SystemMetrics)
async def get_metrics(key_data: dict = Depends(verify_api_key)):
    """Get detailed system metrics"""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory usage
        memory = psutil.virtual_memory()

        # Disk usage
        disk = psutil.disk_usage('/')
        disk_usage = {
            "total": disk.total / 1024 / 1024 / 1024,  # GB
            "used": disk.used / 1024 / 1024 / 1024,   # GB
            "free": disk.free / 1024 / 1024 / 1024,   # GB
            "percent": (disk.used / disk.total) * 100
        }

        # Network connections
        network_connections = len(psutil.net_connections())

        return SystemMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_available=memory.available / 1024 / 1024 / 1024,  # GB
            memory_total=memory.total / 1024 / 1024 / 1024,          # GB
            disk_usage=disk_usage,
            network_connections=network_connections,
            timestamp=time.time()
        )
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to collect metrics: {str(e)}")

@app.get("/logs")
async def get_logs(lines: int = 100, key_data: dict = Depends(verify_api_key)):
    """Get recent log entries"""
    try:
        # This is a simplified implementation
        # In production, you might want to read from actual log files
        return {
            "message": f"Log endpoint - would return last {lines} log entries",
            "note": "Log file integration to be implemented based on logging configuration"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get logs: {str(e)}")

# Configuration endpoints
@app.get("/config")
async def get_config(key_data: dict = Depends(verify_api_key)):
    """Get current configuration"""
    try:
        return config_manager.get_config_dict()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {str(e)}")

@app.get("/config/schema")
async def get_config_schema(key_data: dict = Depends(verify_api_key)):
    """Get configuration schema"""
    try:
        return config_manager.get_config_schema()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get configuration schema: {str(e)}")

@app.get("/config/{section}")
async def get_config_section(section: str, key_data: dict = Depends(verify_api_key)):
    """Get specific configuration section"""
    try:
        config_dict = config_manager.get_config_dict()
        if section not in config_dict:
            raise HTTPException(status_code=404, detail=f"Configuration section '{section}' not found")
        return config_dict[section]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get configuration section: {str(e)}")

@app.get("/config/{section}/{key}")
async def get_config_value(section: str, key: str, key_data: dict = Depends(verify_api_key)):
    """Get specific configuration value"""
    try:
        value = config_manager.get_config_value(section, key)
        if value is None:
            raise HTTPException(status_code=404, detail=f"Configuration value '{section}.{key}' not found")
        return {"section": section, "key": key, "value": value}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get configuration value: {str(e)}")

@app.post("/config")
async def update_config(config_update: ConfigUpdate, key_data: dict = Depends(verify_api_key)):
    """Update configuration"""
    try:
        # Validate configuration update
        if not request_validator.validate_config_update(
            config_update.section, config_update.key, config_update.value
        ):
            raise HTTPException(status_code=400, detail="Invalid configuration parameters")

        # Check permissions
        if not security_middleware.check_permissions(key_data, "write"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        success = config_manager.update_config(
            config_update.section,
            config_update.key,
            config_update.value
        )
        if success:
            audit_logger.log_event("config_update", key_data.get("name", "unknown"), {
                "section": config_update.section,
                "key": config_update.key,
                "value": config_update.value
            })
            return {
                "message": f"Configuration updated: {config_update.section}.{config_update.key} = {config_update.value}",
                "success": True
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to update configuration")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update configuration: {str(e)}")

@app.post("/config/import")
async def import_config(config_json: str, key_data: dict = Depends(verify_api_key)):
    """Import configuration from JSON"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "write"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        success = config_manager.import_config(config_json)
        if success:
            audit_logger.log_event("config_import", key_data.get("name", "unknown"))
            return {"message": "Configuration imported successfully", "success": True}
        else:
            raise HTTPException(status_code=400, detail="Failed to import configuration")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to import configuration: {str(e)}")

@app.get("/config/export")
async def export_config(key_data: dict = Depends(verify_api_key)):
    """Export current configuration as JSON"""
    try:
        return {"config": config_manager.export_config()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export configuration: {str(e)}")

@app.delete("/config")
async def reset_config(section: Optional[str] = None, key: Optional[str] = None, key_data: dict = Depends(verify_api_key)):
    """Reset configuration to defaults"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "write"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        success = config_manager.reset_config(section, key)
        if success:
            if section is None:
                message = "All configuration reset to defaults"
            elif key is None:
                message = f"Configuration section '{section}' reset to defaults"
            else:
                message = f"Configuration value '{section}.{key}' reset to default"

            audit_logger.log_event("config_reset", key_data.get("name", "unknown"), {
                "section": section,
                "key": key
            })
            return {"message": message, "success": True}
        else:
            raise HTTPException(status_code=400, detail="Failed to reset configuration")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset configuration: {str(e)}")

# Service control endpoints
@app.post("/service/start")
async def start_service(key_data: dict = Depends(verify_api_key)):
    """Start the voice service"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "write"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        if not manager.websocket_server_running:
            await manager.start_websocket_server()
            audit_logger.log_event("service_start", key_data.get("name", "unknown"))
            return {"message": "Voice service started successfully"}
        else:
            return {"message": "Voice service is already running"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start service: {str(e)}")

@app.post("/service/stop")
async def stop_service(key_data: dict = Depends(verify_api_key)):
    """Stop the voice service"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "write"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        if manager.websocket_server_running:
            await manager.stop_websocket_server()
            audit_logger.log_event("service_stop", key_data.get("name", "unknown"))
            return {"message": "Voice service stopped successfully"}
        else:
            return {"message": "Voice service is already stopped"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop service: {str(e)}")

@app.post("/service/restart")
async def restart_service(key_data: dict = Depends(verify_api_key)):
    """Restart the voice service"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "write"):
            raise HTTPException(status_code=403, detail="Insufficient permissions")

        await manager.stop_websocket_server()
        await asyncio.sleep(1)
        await manager.start_websocket_server()
        audit_logger.log_event("service_restart", key_data.get("name", "unknown"))
        return {"message": "Voice service restarted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to restart service: {str(e)}")

# Security management endpoints
@app.get("/security/audit")
async def get_audit_logs(limit: int = 100, key_data: dict = Depends(verify_api_key)):
    """Get security audit logs"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "admin"):
            raise HTTPException(status_code=403, detail="Admin permissions required")

        return {"events": audit_logger.get_recent_events(limit)}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get audit logs: {str(e)}")

@app.post("/security/api-key")
async def create_api_key(name: str, permissions: List[str] = None, key_data: dict = Depends(verify_api_key)):
    """Create a new API key"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "admin"):
            raise HTTPException(status_code=403, detail="Admin permissions required")

        if permissions is None:
            permissions = ["read", "write"]

        new_key = api_key_manager.generate_api_key(name, permissions)
        audit_logger.log_event("api_key_created", key_data.get("name", "unknown"), {
            "new_key_name": name,
            "permissions": permissions
        })

        return {
            "api_key": new_key,
            "name": name,
            "permissions": permissions,
            "message": "API key created successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create API key: {str(e)}")

@app.delete("/security/api-key/{api_key}")
async def revoke_api_key(api_key: str, key_data: dict = Depends(verify_api_key)):
    """Revoke an API key"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "admin"):
            raise HTTPException(status_code=403, detail="Admin permissions required")

        success = api_key_manager.revoke_api_key(api_key)
        if success:
            audit_logger.log_event("api_key_revoked", key_data.get("name", "unknown"), {
                "revoked_key": api_key[:8] + "..."
            })
            return {"message": "API key revoked successfully"}
        else:
            raise HTTPException(status_code=404, detail="API key not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to revoke API key: {str(e)}")

@app.get("/security/api-keys")
async def list_api_keys(key_data: dict = Depends(verify_api_key)):
    """List all API keys (without revealing the actual keys)"""
    try:
        # Check permissions
        if not security_middleware.check_permissions(key_data, "admin"):
            raise HTTPException(status_code=403, detail="Admin permissions required")

        keys_info = []
        for api_key, info in api_key_manager.api_keys.items():
            keys_info.append({
                "key_preview": api_key[:8] + "...",
                "name": info["name"],
                "permissions": info["permissions"],
                "created_at": info["created_at"].isoformat(),
                "last_used": info["last_used"].isoformat() if info["last_used"] else None,
                "usage_count": info["usage_count"],
                "active": info["active"]
            })

        return {"api_keys": keys_info}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list API keys: {str(e)}")

# WebSocket proxy endpoint
@app.websocket("/ws/voice")
async def websocket_voice_proxy(websocket: WebSocket, token: Optional[str] = None):
    """WebSocket proxy to the voice streaming server"""
    # Authenticate WebSocket connection
    if token:
        authenticated = await security_middleware.verify_websocket_auth(token)
        if not authenticated:
            await websocket.close(code=1008, reason="Authentication failed")
            return
    else:
        # For now, allow unauthenticated WebSocket connections
        # In production, you might want to require authentication
        logger.warning("WebSocket connection without authentication")

    await manager.connect(websocket)

    try:
        # Connect to internal WebSocket server
        internal_ws_uri = f"ws://{settings.websocket_host}:{settings.websocket_port}"

        async with websockets.connect(internal_ws_uri) as internal_ws:
            # Create bidirectional proxy
            async def proxy_to_internal():
                try:
                    async for message in websocket.iter_text():
                        await internal_ws.send(message)
                except WebSocketDisconnect:
                    pass

            async def proxy_from_internal():
                try:
                    async for message in internal_ws:
                        await websocket.send_text(message)
                except websockets.exceptions.ConnectionClosed:
                    pass

            # Run both proxy directions concurrently
            await asyncio.gather(
                proxy_to_internal(),
                proxy_from_internal(),
                return_exceptions=True
            )

    except Exception as e:
        logger.error(f"WebSocket proxy error: {e}")
    finally:
        manager.disconnect(websocket)

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,
        log_level="info"
    )
