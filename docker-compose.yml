version: '3.8'

services:
  voice-assistant-api:
    build: .
    container_name: voice-assistant-api
    ports:
      - "8000:8000"  # API port
      - "8765:8765"  # WebSocket port
    environment:
      # API Configuration
      - VOICE_API_API_HOST=0.0.0.0
      - VOICE_API_API_PORT=8000
      - VOICE_API_WEBSOCKET_HOST=localhost
      - VOICE_API_WEBSOCKET_PORT=8765
      - VOICE_API_API_KEY=${API_KEY:-your-secure-api-key-here}
      - VOICE_API_CORS_ORIGINS=["*"]

      # Application Configuration
      - PYTHONPATH=/app
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - USE_SIMPLE_MODE=${USE_SIMPLE_MODE:-false}

      # Optional: Override default configuration
      - RAG_MODEL_NAME=${RAG_MODEL_NAME:-gpt-4o}
      - TRANSCRIPTION_MODEL=${TRANSCRIPTION_MODEL:-gpt-4o-transcribe}
      - ENABLE_TTS=${ENABLE_TTS:-true}
      
    volumes:
      # Mount configuration directory for persistence
      - ./config:/app/config
      # Mount RAG index for persistence
      - ./rag_index:/app/rag_index
      # Mount logs directory
      - voice_logs:/app/logs
      # Optional: Mount custom forms
      - ./forms.json:/app/forms.json:ro
      # Optional: Mount PDF files
      - ./ZI102.pdf:/app/ZI102.pdf:ro
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: voice-assistant-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - voice-assistant-api
    restart: unless-stopped
    profiles:
      - production

volumes:
  voice_logs:
    driver: local

networks:
  default:
    name: voice-assistant-network
