"""
Example usage of the Voice Assistant API

This script demonstrates how to use the Voice Assistant API client
to interact with a remote voice assistant server.
"""

import time
import json
from api_client import VoiceAssistantClient, VoiceWebSocketClient, VoiceAssistantAPIError


def main():
    """Main example function"""
    print("🎙️ Voice Assistant API Example")
    print("=" * 40)
    
    # Configuration
    api_url = "http://localhost:8000"
    api_key = "your-api-key-here"  # Replace with actual API key
    
    # Initialize API client
    try:
        client = VoiceAssistantClient(base_url=api_url, api_key=api_key)
        print(f"✅ Connected to API server at {api_url}")
    except Exception as e:
        print(f"❌ Failed to connect: {e}")
        return
    
    # Example 1: Health Check
    print("\n1️⃣ Health Check")
    try:
        health = client.health_check()
        print(f"   Status: {health['status']}")
        print(f"   Uptime: {health.get('uptime', 0):.1f} seconds")
        print(f"   Version: {health.get('version', 'unknown')}")
    except VoiceAssistantAPIError as e:
        print(f"   ❌ Health check failed: {e}")
        return
    
    # Example 2: Service Status
    print("\n2️⃣ Service Status")
    try:
        status = client.get_status()
        print(f"   Service: {status['status']}")
        print(f"   WebSocket Server: {'✅' if status['websocket_server_running'] else '❌'}")
        print(f"   Active Connections: {status['active_connections']}")
        
        if 'memory_usage' in status:
            memory = status['memory_usage']
            print(f"   Memory Usage: {memory['rss']:.1f} MB ({memory['percent']:.1f}%)")
        
        if 'cpu_usage' in status:
            print(f"   CPU Usage: {status['cpu_usage']:.1f}%")
            
    except VoiceAssistantAPIError as e:
        print(f"   ❌ Status check failed: {e}")
    
    # Example 3: Configuration Management
    print("\n3️⃣ Configuration Management")
    try:
        # Get current configuration
        config = client.get_config()
        print(f"   Current model: {config['rag']['model_name']}")
        print(f"   TTS enabled: {config['tts']['enable_tts']}")
        
        # Update a configuration value
        print("   Updating transcription temperature...")
        result = client.update_config("transcription", "temperature", "0.1")
        if result.get('success'):
            print("   ✅ Configuration updated successfully")
        else:
            print("   ❌ Configuration update failed")
        
        # Get specific configuration value
        temp_value = client.get_config_value("transcription", "temperature")
        print(f"   New temperature value: {temp_value}")
        
    except VoiceAssistantAPIError as e:
        print(f"   ❌ Configuration error: {e}")
    
    # Example 4: System Metrics
    print("\n4️⃣ System Metrics")
    try:
        metrics = client.get_metrics()
        print(f"   CPU: {metrics['cpu_percent']:.1f}%")
        print(f"   Memory: {metrics['memory_percent']:.1f}% ({metrics['memory_available']:.1f} GB available)")
        print(f"   Network Connections: {metrics['network_connections']}")
        
        if 'disk_usage' in metrics:
            disk = metrics['disk_usage']
            print(f"   Disk: {disk['percent']:.1f}% used ({disk['used']:.1f} GB / {disk['total']:.1f} GB)")
            
    except VoiceAssistantAPIError as e:
        print(f"   ❌ Metrics error: {e}")
    
    # Example 5: Configuration Export/Import
    print("\n5️⃣ Configuration Export/Import")
    try:
        # Export configuration
        config_json = client.export_config()
        print("   ✅ Configuration exported")
        
        # Parse and display some settings
        config_data = json.loads(config_json)
        print(f"   Exported {len(config_data)} configuration sections")
        
        # You could save this to a file:
        # with open('exported_config.json', 'w') as f:
        #     f.write(config_json)
        
        # Import would work like this:
        # result = client.import_config(config_json)
        
    except VoiceAssistantAPIError as e:
        print(f"   ❌ Export/Import error: {e}")
    
    # Example 6: Service Control
    print("\n6️⃣ Service Control")
    try:
        # Check current status
        status = client.get_status()
        is_running = status['websocket_server_running']
        
        if is_running:
            print("   Service is running")
            # Optionally restart
            # result = client.restart_service()
            # print(f"   Restart result: {result['message']}")
        else:
            print("   Service is not running, starting...")
            result = client.start_service()
            print(f"   Start result: {result['message']}")
            
    except VoiceAssistantAPIError as e:
        print(f"   ❌ Service control error: {e}")
    
    # Example 7: Security Management (Admin only)
    print("\n7️⃣ Security Management")
    try:
        # Try to get audit logs (requires admin permissions)
        audit_logs = client.get_audit_logs(limit=5)
        print(f"   Retrieved {len(audit_logs)} audit log entries")
        
        for log in audit_logs[-3:]:  # Show last 3 entries
            print(f"   - {log['timestamp']}: {log['event_type']} by {log.get('api_key', 'unknown')}")
        
        # Try to list API keys (requires admin permissions)
        api_keys = client.list_api_keys()
        print(f"   Found {len(api_keys)} API keys")
        
        for key_info in api_keys:
            print(f"   - {key_info['name']}: {key_info['key_preview']} ({'active' if key_info['active'] else 'inactive'})")
        
    except VoiceAssistantAPIError as e:
        if "permissions" in str(e).lower():
            print("   ℹ️ Security features require admin permissions")
        else:
            print(f"   ❌ Security error: {e}")
    
    # Example 8: WebSocket Voice Streaming
    print("\n8️⃣ WebSocket Voice Streaming")
    try:
        # Create WebSocket client
        ws_url = api_url.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws/voice'
        ws_client = VoiceWebSocketClient(ws_url, api_key)
        
        # Set up message handlers
        def on_transcription(data):
            if data.get('transcription'):
                print(f"   🎤 User: {data['transcription']}")
            if data.get('ai_response'):
                print(f"   🤖 AI: {data['ai_response']}")
        
        def on_status(data):
            status = data.get('status', 'unknown')
            if status == 'recording':
                print(f"   📊 Recording... (RMS: {data.get('rms', 0):.3f})")
            elif status == 'processing':
                print("   ⚙️ Processing audio...")
        
        def on_error(error):
            print(f"   ❌ WebSocket error: {error}")
        
        def on_connect():
            print("   ✅ WebSocket connected")
        
        def on_disconnect(code, msg):
            print(f"   🔌 WebSocket disconnected: {code} - {msg}")
        
        # Register handlers
        ws_client.on_message("transcription_complete", on_transcription)
        ws_client.on_message("default", on_status)  # Catch-all for other messages
        ws_client.on_error(on_error)
        ws_client.on_connect(on_connect)
        ws_client.on_disconnect(on_disconnect)
        
        # Connect
        print("   Connecting to WebSocket...")
        ws_client.connect()
        
        # Simulate some interaction
        print("   Starting recording session...")
        ws_client.start_recording()
        
        # In a real application, you would:
        # 1. Capture audio from microphone
        # 2. Convert to base64
        # 3. Send via ws_client.send_audio_chunk(base64_audio)
        
        # For this example, just wait a bit
        time.sleep(2)
        
        print("   Stopping recording session...")
        ws_client.stop_recording()
        
        # Wait a bit more for any responses
        time.sleep(1)
        
        # Disconnect
        ws_client.disconnect()
        
    except VoiceAssistantAPIError as e:
        print(f"   ❌ WebSocket error: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected WebSocket error: {e}")
    
    print("\n✅ API Example Complete!")
    print("\nNext Steps:")
    print("- Integrate voice recording for real audio streaming")
    print("- Build a custom UI using the API client")
    print("- Deploy the API server to a remote machine")
    print("- Set up monitoring and alerting")


if __name__ == "__main__":
    main()
