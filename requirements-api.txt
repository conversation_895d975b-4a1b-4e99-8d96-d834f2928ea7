# Voice Assistant API Requirements
# Streamlined requirements without WebRTC dependencies

# Core API Framework
fastapi==0.115.6
uvicorn==0.32.1
python-multipart==0.0.20
pydantic-settings==2.7.1

# WebSocket Support
websockets==12.0

# System Monitoring
psutil==6.1.1

# HTTP Client
requests==2.32.3
httpx==0.28.1

# LangChain and AI
langchain==0.3.25
langchain_community==0.3.24
langchain-openai==0.3.18
langchain-litellm==0.2.1

# Document Processing
pypdf==5.5.0
tiktoken==0.9.0

# Vector Database
faiss-cpu==1.11.0

# Utilities
python-dotenv==1.0.1
pydantic==2.10.6
typing_extensions==4.12.2

# Windows Support
pywin32==310

# Optional: Streamlit (without WebRTC)
streamlit>=1.28.0

# Core Dependencies
annotated-types==0.7.0
anyio==4.8.0
certifi==2025.1.31
charset-normalizer==3.4.1
h11==0.14.0
httpcore==1.0.7
idna==3.10
sniffio==1.3.1
urllib3==2.3.0
