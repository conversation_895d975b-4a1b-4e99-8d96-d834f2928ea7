"""
API-Compatible Streaming Voice Assistant Streamlit Application

This application connects to the Voice Assistant API server instead of running locally.
"""

import streamlit as st
import streamlit.components.v1 as components
import json
import time
from api_client import VoiceAssistantClient, VoiceAssistantAPIError

# Page configuration
st.set_page_config(
    page_title="Voice Assistant API Client",
    page_icon="🎙️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'api_client' not in st.session_state:
    st.session_state.api_client = None
if 'connected' not in st.session_state:
    st.session_state.connected = False
if 'config' not in st.session_state:
    st.session_state.config = {}

# Sidebar for API configuration
st.sidebar.title("🔧 API Configuration")

# API connection settings
api_url = st.sidebar.text_input(
    "API Server URL",
    value="http://localhost:8000",
    help="URL of the Voice Assistant API server"
)

api_key = st.sidebar.text_input(
    "API Key",
    type="password",
    help="API key for authentication"
)

# Connection button
if st.sidebar.button("🔌 Connect to API"):
    try:
        client = VoiceAssistantClient(base_url=api_url, api_key=api_key)
        health = client.health_check()
        
        if health.get('status') == 'healthy':
            st.session_state.api_client = client
            st.session_state.connected = True
            st.sidebar.success("✅ Connected to API server")
            
            # Load configuration
            try:
                st.session_state.config = client.get_config()
            except Exception as e:
                st.sidebar.warning(f"Could not load config: {e}")
        else:
            st.sidebar.error("❌ API server is not healthy")
            
    except VoiceAssistantAPIError as e:
        st.sidebar.error(f"❌ Connection failed: {e}")
    except Exception as e:
        st.sidebar.error(f"❌ Unexpected error: {e}")

# Disconnect button
if st.session_state.connected and st.sidebar.button("🔌 Disconnect"):
    st.session_state.api_client = None
    st.session_state.connected = False
    st.session_state.config = {}
    st.sidebar.info("Disconnected from API server")

# Main content
st.title("🎙️ Voice Assistant API Client")

if not st.session_state.connected:
    st.warning("⚠️ Please connect to the API server using the sidebar")
    st.markdown("""
    ## Getting Started
    
    1. **Start the API Server**: Run `python start_api_server.py` or use Docker
    2. **Enter API URL**: Default is `http://localhost:8000`
    3. **Enter API Key**: Check server logs for the default key or create a new one
    4. **Click Connect**: Establish connection to the API server
    
    ## Features
    
    - 🎙️ **Voice Streaming**: Real-time voice chat through API
    - ⚙️ **Configuration Management**: Update settings remotely
    - 📊 **System Monitoring**: View server status and metrics
    - 🔐 **Security**: API key authentication and audit logs
    """)
    st.stop()

# Connected - show main interface
client = st.session_state.api_client

# Status display
col1, col2, col3 = st.columns(3)

try:
    status = client.get_status()
    
    with col1:
        st.metric(
            "Service Status",
            status.get('status', 'unknown').title(),
            delta=None
        )
    
    with col2:
        st.metric(
            "Active Connections",
            status.get('active_connections', 0),
            delta=None
        )
    
    with col3:
        uptime = status.get('uptime', 0)
        uptime_str = f"{uptime/3600:.1f}h" if uptime > 3600 else f"{uptime/60:.1f}m"
        st.metric(
            "Uptime",
            uptime_str,
            delta=None
        )

except Exception as e:
    st.error(f"Failed to get status: {e}")

# Tabs for different features
tab1, tab2, tab3, tab4 = st.tabs(["🎙️ Voice Chat", "⚙️ Configuration", "📊 Monitoring", "🔐 Security"])

with tab1:
    st.subheader("Voice Streaming Chat")
    
    # WebSocket URL for voice streaming
    ws_url = api_url.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws/voice'
    if api_key:
        ws_url += f'?token={api_key}'
    
    # HTML component for voice streaming (modified to use API)
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            .voice-container {{
                padding: 20px;
                border: 2px solid #ddd;
                border-radius: 10px;
                margin: 10px 0;
                background-color: #f9f9f9;
            }}
            .status-indicator {{
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
                font-weight: bold;
            }}
            .status-idle {{ background-color: #e3f2fd; color: #1976d2; }}
            .status-recording {{ background-color: #ffebee; color: #d32f2f; }}
            .status-processing {{ background-color: #fff3e0; color: #f57c00; }}
            .chat-area {{
                height: 300px;
                overflow-y: auto;
                border: 1px solid #ccc;
                padding: 10px;
                margin: 10px 0;
                background-color: white;
            }}
            .message {{
                margin: 10px 0;
                padding: 8px;
                border-radius: 5px;
            }}
            .user-message {{
                background-color: #e3f2fd;
                text-align: right;
            }}
            .ai-message {{
                background-color: #f1f8e9;
            }}
            button {{
                padding: 10px 20px;
                margin: 5px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            }}
            .start-btn {{
                background-color: #4caf50;
                color: white;
            }}
            .stop-btn {{
                background-color: #f44336;
                color: white;
            }}
            .audio-level {{
                width: 100%;
                height: 20px;
                background-color: #ddd;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }}
            .audio-level-bar {{
                height: 100%;
                background-color: #4caf50;
                transition: width 0.1s;
            }}
        </style>
    </head>
    <body>
        <div class="voice-container">
            <div id="status" class="status-indicator status-idle">Ready to connect</div>
            
            <div class="audio-level">
                <div id="audioLevel" class="audio-level-bar" style="width: 0%"></div>
            </div>
            
            <button id="startBtn" class="start-btn">🎙️ Start Streaming</button>
            <button id="stopBtn" class="stop-btn" disabled>⏹️ Stop Streaming</button>
            
            <div id="chatArea" class="chat-area">
                <div class="message ai-message">
                    <strong>AI Assistant:</strong> Click "Start Streaming" to begin voice chat!
                </div>
            </div>
        </div>

        <script>
            let websocket = null;
            let mediaRecorder = null;
            let audioContext = null;
            let isStreaming = false;
            let isAISpeaking = false;

            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const statusDiv = document.getElementById('status');
            const chatArea = document.getElementById('chatArea');
            const audioLevelBar = document.getElementById('audioLevel');

            function updateStatus(type, message) {{
                statusDiv.className = `status-indicator status-${{type}}`;
                statusDiv.textContent = message;
            }}

            function addMessage(sender, message) {{
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${{sender === 'User' ? 'user-message' : 'ai-message'}}`;
                messageDiv.innerHTML = `<strong>${{sender}}:</strong> ${{message}}`;
                chatArea.appendChild(messageDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
            }}

            function connectWebSocket() {{
                const wsUrl = '{ws_url}';
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function() {{
                    updateStatus('idle', 'Connected - ready to stream');
                    startBtn.disabled = false;
                }};

                websocket.onmessage = function(event) {{
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'transcription_complete') {{
                        if (data.transcription) {{
                            addMessage('User', data.transcription);
                        }}
                        if (data.ai_response) {{
                            addMessage('AI Assistant', data.ai_response);
                        }}
                        if (data.ai_audio_base64) {{
                            playAudioResponse(data.ai_audio_base64);
                        }}
                    }} else if (data.status === 'recording') {{
                        updateStatus('recording', 'Listening...');
                        audioLevelBar.style.width = (data.rms * 100) + '%';
                    }} else if (data.status === 'processing') {{
                        updateStatus('processing', 'Processing...');
                    }}
                }};

                websocket.onerror = function(error) {{
                    updateStatus('error', 'WebSocket error');
                    console.error('WebSocket error:', error);
                }};

                websocket.onclose = function() {{
                    updateStatus('idle', 'Disconnected');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    isStreaming = false;
                }};
            }}

            function playAudioResponse(base64Audio) {{
                isAISpeaking = true;
                const audioData = atob(base64Audio);
                const arrayBuffer = new ArrayBuffer(audioData.length);
                const uint8Array = new Uint8Array(arrayBuffer);
                
                for (let i = 0; i < audioData.length; i++) {{
                    uint8Array[i] = audioData.charCodeAt(i);
                }}
                
                const blob = new Blob([arrayBuffer], {{ type: 'audio/wav' }});
                const audioUrl = URL.createObjectURL(blob);
                const audio = new Audio(audioUrl);
                
                audio.onended = function() {{
                    isAISpeaking = false;
                    URL.revokeObjectURL(audioUrl);
                }};
                
                audio.play().catch(e => {{
                    console.error('Audio play error:', e);
                    isAISpeaking = false;
                }});
            }}

            async function startStreaming() {{
                try {{
                    const stream = await navigator.mediaDevices.getUserMedia({{ audio: true }});
                    audioContext = new AudioContext();
                    const source = audioContext.createMediaStreamSource(stream);
                    const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                    
                    source.connect(scriptProcessor);
                    scriptProcessor.connect(audioContext.destination);

                    scriptProcessor.onaudioprocess = function(event) {{
                        if (isAISpeaking) return;

                        const inputBuffer = event.inputBuffer;
                        const inputData = inputBuffer.getChannelData(0);
                        const float32Array = new Float32Array(inputData);
                        const arrayBuffer = float32Array.buffer;
                        const uint8Array = new Uint8Array(arrayBuffer);
                        const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));

                        if (websocket && websocket.readyState === WebSocket.OPEN) {{
                            websocket.send(JSON.stringify({{
                                type: 'audio_chunk',
                                audio_data: base64Audio
                            }}));
                        }}
                    }};

                    isStreaming = true;
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                    updateStatus('recording', 'Streaming active - speak now');

                    if (websocket && websocket.readyState === WebSocket.OPEN) {{
                        websocket.send(JSON.stringify({{ type: 'start_recording' }}));
                    }}

                }} catch (error) {{
                    console.error('Error starting stream:', error);
                    updateStatus('error', 'Failed to start streaming');
                    alert('Error accessing microphone: ' + error.message);
                }}
            }}

            function stopStreaming() {{
                if (audioContext) {{
                    audioContext.close();
                    audioContext = null;
                }}
                
                if (websocket && websocket.readyState === WebSocket.OPEN) {{
                    websocket.send(JSON.stringify({{ type: 'stop_recording' }}));
                }}
                
                isStreaming = false;
                startBtn.disabled = false;
                stopBtn.disabled = true;
                updateStatus('idle', 'Streaming stopped');
                audioLevelBar.style.width = '0%';
            }}

            startBtn.addEventListener('click', startStreaming);
            stopBtn.addEventListener('click', stopStreaming);

            // Connect on load
            connectWebSocket();
        </script>
    </body>
    </html>
    """
    
    components.html(html_content, height=600, scrolling=False)

with tab2:
    st.subheader("Configuration Management")
    
    if st.session_state.config:
        # Configuration editor
        config = st.session_state.config
        
        # Select section to edit
        sections = list(config.keys())
        selected_section = st.selectbox("Configuration Section", sections)
        
        if selected_section and selected_section in config:
            st.write(f"**{selected_section.upper()} Configuration**")
            
            section_config = config[selected_section]
            
            # Display current values and allow editing
            for key, value in section_config.items():
                col1, col2, col3 = st.columns([2, 2, 1])
                
                with col1:
                    st.write(f"**{key}**")
                
                with col2:
                    new_value = st.text_input(
                        f"Value for {key}",
                        value=str(value),
                        key=f"config_{selected_section}_{key}"
                    )
                
                with col3:
                    if st.button("Update", key=f"update_{selected_section}_{key}"):
                        try:
                            result = client.update_config(selected_section, key, new_value)
                            if result.get('success'):
                                st.success(f"Updated {key}")
                                # Refresh config
                                st.session_state.config = client.get_config()
                                st.experimental_rerun()
                            else:
                                st.error("Update failed")
                        except Exception as e:
                            st.error(f"Update error: {e}")
        
        # Export/Import configuration
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📤 Export Configuration"):
                try:
                    config_json = client.export_config()
                    st.download_button(
                        "Download Config",
                        config_json,
                        "voice_assistant_config.json",
                        "application/json"
                    )
                except Exception as e:
                    st.error(f"Export error: {e}")
        
        with col2:
            uploaded_file = st.file_uploader("📥 Import Configuration", type="json")
            if uploaded_file and st.button("Import"):
                try:
                    config_data = uploaded_file.read().decode()
                    result = client.import_config(config_data)
                    if result.get('success'):
                        st.success("Configuration imported")
                        st.session_state.config = client.get_config()
                        st.experimental_rerun()
                    else:
                        st.error("Import failed")
                except Exception as e:
                    st.error(f"Import error: {e}")

with tab3:
    st.subheader("System Monitoring")
    
    # Refresh button
    if st.button("🔄 Refresh Metrics"):
        st.experimental_rerun()
    
    try:
        metrics = client.get_metrics()
        
        # System metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("CPU Usage", f"{metrics.get('cpu_percent', 0):.1f}%")
        
        with col2:
            st.metric("Memory Usage", f"{metrics.get('memory_percent', 0):.1f}%")
        
        with col3:
            memory_gb = metrics.get('memory_available', 0)
            st.metric("Available Memory", f"{memory_gb:.1f} GB")
        
        with col4:
            st.metric("Network Connections", metrics.get('network_connections', 0))
        
        # Disk usage
        if 'disk_usage' in metrics:
            disk = metrics['disk_usage']
            st.subheader("Disk Usage")
            st.progress(disk.get('percent', 0) / 100)
            st.write(f"Used: {disk.get('used', 0):.1f} GB / {disk.get('total', 0):.1f} GB")
        
    except Exception as e:
        st.error(f"Failed to get metrics: {e}")

with tab4:
    st.subheader("Security Management")
    
    # Service control
    st.write("**Service Control**")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("▶️ Start Service"):
            try:
                result = client.start_service()
                st.success(result.get('message', 'Service started'))
            except Exception as e:
                st.error(f"Start error: {e}")
    
    with col2:
        if st.button("⏹️ Stop Service"):
            try:
                result = client.stop_service()
                st.success(result.get('message', 'Service stopped'))
            except Exception as e:
                st.error(f"Stop error: {e}")
    
    with col3:
        if st.button("🔄 Restart Service"):
            try:
                result = client.restart_service()
                st.success(result.get('message', 'Service restarted'))
            except Exception as e:
                st.error(f"Restart error: {e}")
    
    # Audit logs (if admin)
    try:
        audit_logs = client.get_audit_logs(limit=50)
        if audit_logs:
            st.write("**Recent Audit Events**")
            for event in audit_logs[-10:]:  # Show last 10 events
                st.write(f"**{event.get('timestamp', '')}** - {event.get('event_type', '')} by {event.get('api_key', 'unknown')}")
    except Exception as e:
        st.info("Audit logs require admin permissions")

# Footer
st.markdown("---")
st.markdown("🎙️ **Voice Assistant API Client** - Connect to remote voice assistant servers")
