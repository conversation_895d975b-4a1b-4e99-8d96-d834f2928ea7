"""
Security utilities for the Voice Assistant API

Provides authentication, authorization, and security middleware.
"""

import hashlib
import hmac
import secrets
import time
from typing import Dict, Optional, List
from datetime import datetime, timedelta

from fastapi import HTTPException, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials


class APIKeyManager:
    """Manages API keys and authentication"""
    
    def __init__(self):
        self.api_keys: Dict[str, Dict] = {}
        self.rate_limits: Dict[str, List[float]] = {}
        
    def generate_api_key(self, name: str, permissions: List[str] = None) -> str:
        """Generate a new API key"""
        if permissions is None:
            permissions = ["read", "write"]
            
        api_key = secrets.token_urlsafe(32)
        self.api_keys[api_key] = {
            "name": name,
            "permissions": permissions,
            "created_at": datetime.utcnow(),
            "last_used": None,
            "usage_count": 0,
            "active": True
        }
        return api_key
    
    def validate_api_key(self, api_key: str) -> Optional[Dict]:
        """Validate an API key and return its metadata"""
        if api_key not in self.api_keys:
            return None
            
        key_data = self.api_keys[api_key]
        if not key_data.get("active", False):
            return None
            
        # Update usage statistics
        key_data["last_used"] = datetime.utcnow()
        key_data["usage_count"] += 1
        
        return key_data
    
    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key"""
        if api_key in self.api_keys:
            self.api_keys[api_key]["active"] = False
            return True
        return False
    
    def check_rate_limit(self, api_key: str, max_requests: int = 100, window_seconds: int = 3600) -> bool:
        """Check if API key is within rate limits"""
        now = time.time()
        window_start = now - window_seconds
        
        if api_key not in self.rate_limits:
            self.rate_limits[api_key] = []
        
        # Remove old requests outside the window
        self.rate_limits[api_key] = [
            req_time for req_time in self.rate_limits[api_key] 
            if req_time > window_start
        ]
        
        # Check if under limit
        if len(self.rate_limits[api_key]) >= max_requests:
            return False
        
        # Add current request
        self.rate_limits[api_key].append(now)
        return True


class SecurityMiddleware:
    """Security middleware for request validation"""
    
    def __init__(self, api_key_manager: APIKeyManager):
        self.api_key_manager = api_key_manager
        self.security = HTTPBearer()
    
    async def verify_api_key(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        """Verify API key authentication"""
        api_key = credentials.credentials
        
        # Validate API key
        key_data = self.api_key_manager.validate_api_key(api_key)
        if not key_data:
            raise HTTPException(
                status_code=401,
                detail="Invalid or inactive API key"
            )
        
        # Check rate limits
        if not self.api_key_manager.check_rate_limit(api_key):
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded"
            )
        
        return key_data
    
    async def verify_websocket_auth(self, token: str) -> bool:
        """Verify WebSocket authentication token"""
        # For WebSocket connections, we can use the same API key validation
        key_data = self.api_key_manager.validate_api_key(token)
        return key_data is not None and key_data.get("active", False)
    
    def check_permissions(self, key_data: Dict, required_permission: str) -> bool:
        """Check if API key has required permissions"""
        permissions = key_data.get("permissions", [])
        return required_permission in permissions or "admin" in permissions


class RequestValidator:
    """Validates and sanitizes incoming requests"""
    
    @staticmethod
    def validate_config_update(section: str, key: str, value: str) -> bool:
        """Validate configuration update parameters"""
        # Define allowed sections and keys
        allowed_sections = {
            "rag": ["model_name", "forms_path", "file_path", "index_path"],
            "audio": ["rate", "pause_threshold"],
            "streaming": ["silence_threshold", "max_silence_duration", "min_audio_duration"],
            "transcription": ["model", "language", "temperature", "prompt"],
            "ui": ["page_title", "layout"],
            "application": ["default_application", "default_form"],
            "agent": ["verbose"],
            "tts": ["enable_tts", "autoplay_audio"],
            "session": ["enable_reset"]
        }
        
        if section not in allowed_sections:
            return False
        
        if key not in allowed_sections[section]:
            return False
        
        # Additional validation based on key type
        if key in ["rate"] and not value.isdigit():
            return False
        
        if key in ["temperature", "silence_threshold", "max_silence_duration", "min_audio_duration", "pause_threshold"]:
            try:
                float(value)
            except ValueError:
                return False
        
        if key in ["verbose", "enable_tts", "autoplay_audio", "enable_reset"]:
            if value.lower() not in ["true", "false", "1", "0", "yes", "no"]:
                return False
        
        return True
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            return ""
        
        # Remove potentially dangerous characters
        sanitized = value.replace("<", "&lt;").replace(">", "&gt;")
        sanitized = sanitized.replace("&", "&amp;").replace('"', "&quot;")
        
        # Limit length
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        return sanitized.strip()


class AuditLogger:
    """Logs security-related events"""
    
    def __init__(self):
        self.events: List[Dict] = []
    
    def log_event(self, event_type: str, api_key: str, details: Dict = None):
        """Log a security event"""
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "api_key": api_key[:8] + "..." if api_key else None,  # Partial key for privacy
            "details": details or {}
        }
        self.events.append(event)
        
        # Keep only last 1000 events
        if len(self.events) > 1000:
            self.events = self.events[-1000:]
    
    def get_recent_events(self, limit: int = 100) -> List[Dict]:
        """Get recent security events"""
        return self.events[-limit:]


# Global instances
api_key_manager = APIKeyManager()
security_middleware = SecurityMiddleware(api_key_manager)
request_validator = RequestValidator()
audit_logger = AuditLogger()

# Generate default API key if none exist
if not api_key_manager.api_keys:
    default_key = api_key_manager.generate_api_key("default", ["admin"])
    print(f"Generated default API key: {default_key}")
    print("Please change this key in production!")
