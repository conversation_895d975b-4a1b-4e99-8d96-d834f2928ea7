"""
Simple Voice Assistant API Server

A minimal version that works with basic dependencies only.
Perfect for getting started quickly without complex setup.
"""

import asyncio
import json
import logging
import os
import time
import secrets
from typing import Dict, List, Optional, Any
from datetime import datetime

import uvicorn
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple configuration
class SimpleConfig:
    def __init__(self):
        self.api_host = os.getenv("VOICE_API_API_HOST", "0.0.0.0")
        self.api_port = int(os.getenv("VOICE_API_API_PORT", "8000"))
        self.websocket_host = os.getenv("VOICE_API_WEBSOCKET_HOST", "localhost")
        self.websocket_port = int(os.getenv("VOICE_API_WEBSOCKET_PORT", "8765"))
        self.api_key = os.getenv("VOICE_API_API_KEY", self._generate_default_key())
        self.cors_origins = ["*"]
    
    def _generate_default_key(self):
        key = secrets.token_urlsafe(32)
        print(f"🔑 Generated default API key: {key}")
        print("   Save this key for client connections!")
        return key

# Global configuration
config = SimpleConfig()

# Simple authentication
security = HTTPBearer()

def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Simple API key verification"""
    if credentials.credentials != config.api_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

# Pydantic models
class HealthCheck(BaseModel):
    status: str
    timestamp: float
    version: str = "1.0.0-simple"
    uptime: Optional[float] = None

class ServiceStatus(BaseModel):
    status: str
    websocket_server_running: bool = False
    active_connections: int = 0
    uptime: Optional[float] = None

class ConfigUpdate(BaseModel):
    section: str = Field(..., description="Configuration section name")
    key: str = Field(..., description="Configuration key")
    value: str = Field(..., description="Configuration value")

# Simple WebSocket manager
class SimpleConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.websocket_server_running = False

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def start_websocket_server(self):
        """Simulate starting WebSocket server"""
        self.websocket_server_running = True
        logger.info("WebSocket server simulation started")

    async def stop_websocket_server(self):
        """Simulate stopping WebSocket server"""
        self.websocket_server_running = False
        logger.info("WebSocket server simulation stopped")

manager = SimpleConnectionManager()
app_start_time = time.time()

# FastAPI app
app = FastAPI(
    title="Voice Assistant API (Simple)",
    description="Minimal REST API for Voice Streaming Chatbot",
    version="1.0.0-simple"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health endpoints
@app.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint"""
    uptime = time.time() - app_start_time
    return HealthCheck(
        status="healthy",
        timestamp=time.time(),
        uptime=uptime
    )

@app.get("/status", response_model=ServiceStatus)
async def get_status(api_key: str = Depends(verify_api_key)):
    """Get service status"""
    uptime = time.time() - app_start_time
    return ServiceStatus(
        status="running" if manager.websocket_server_running else "stopped",
        websocket_server_running=manager.websocket_server_running,
        active_connections=len(manager.active_connections),
        uptime=uptime
    )

# Simple configuration endpoints
@app.get("/config")
async def get_config(api_key: str = Depends(verify_api_key)):
    """Get simple configuration"""
    return {
        "api": {
            "host": config.api_host,
            "port": config.api_port,
            "websocket_host": config.websocket_host,
            "websocket_port": config.websocket_port
        },
        "status": "simple_mode",
        "message": "This is a simplified API server. Install full dependencies for complete functionality."
    }

@app.post("/config")
async def update_config(config_update: ConfigUpdate, api_key: str = Depends(verify_api_key)):
    """Update configuration (simulation)"""
    return {
        "message": f"Configuration update simulated: {config_update.section}.{config_update.key} = {config_update.value}",
        "success": True,
        "note": "This is a simulation. Install full dependencies for real configuration management."
    }

# Service control endpoints
@app.post("/service/start")
async def start_service(api_key: str = Depends(verify_api_key)):
    """Start the voice service (simulation)"""
    await manager.start_websocket_server()
    return {"message": "Voice service simulation started"}

@app.post("/service/stop")
async def stop_service(api_key: str = Depends(verify_api_key)):
    """Stop the voice service (simulation)"""
    await manager.stop_websocket_server()
    return {"message": "Voice service simulation stopped"}

@app.post("/service/restart")
async def restart_service(api_key: str = Depends(verify_api_key)):
    """Restart the voice service (simulation)"""
    await manager.stop_websocket_server()
    await asyncio.sleep(1)
    await manager.start_websocket_server()
    return {"message": "Voice service simulation restarted"}

# Simple WebSocket endpoint
@app.websocket("/ws/voice")
async def websocket_voice_endpoint(websocket: WebSocket):
    """Simple WebSocket endpoint"""
    await manager.connect(websocket)
    
    try:
        # Send welcome message
        welcome_msg = {
            "status": "connected",
            "message": "Connected to simple WebSocket server",
            "note": "This is a simulation. Install full dependencies for real voice processing."
        }
        await websocket.send_text(json.dumps(welcome_msg))
        
        async for message in websocket.iter_text():
            try:
                data = json.loads(message)
                message_type = data.get("type", "unknown")
                
                if message_type == "audio_chunk":
                    # Simulate audio processing
                    response = {
                        "status": "processing",
                        "message": "Audio chunk received (simulation)"
                    }
                    await websocket.send_text(json.dumps(response))
                
                elif message_type == "start_recording":
                    response = {
                        "status": "recording_started",
                        "message": "Recording simulation started"
                    }
                    await websocket.send_text(json.dumps(response))
                
                elif message_type == "stop_recording":
                    response = {
                        "status": "transcription_complete",
                        "transcription": "This is a simulation - no real audio processing",
                        "ai_response": "Hello! This is the simple API server. For full voice processing, please install all dependencies."
                    }
                    await websocket.send_text(json.dumps(response))
                
                else:
                    response = {
                        "status": "unknown_message",
                        "message": f"Unknown message type: {message_type}"
                    }
                    await websocket.send_text(json.dumps(response))
                    
            except json.JSONDecodeError:
                error_response = {
                    "status": "error",
                    "message": "Invalid JSON message"
                }
                await websocket.send_text(json.dumps(error_response))
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        manager.disconnect(websocket)

# Info endpoint
@app.get("/info")
async def get_info():
    """Get API information"""
    return {
        "name": "Voice Assistant API (Simple)",
        "version": "1.0.0-simple",
        "description": "Minimal API server for testing and development",
        "features": [
            "Basic REST API",
            "Simple WebSocket connections",
            "Health monitoring",
            "Configuration simulation"
        ],
        "limitations": [
            "No real audio processing",
            "No advanced configuration management",
            "No security features beyond basic API key",
            "No system metrics"
        ],
        "upgrade_instructions": {
            "message": "For full functionality, install complete dependencies",
            "commands": [
                "pip install -r requirements.txt",
                "python start_api_server.py"
            ]
        }
    }

# Startup event
@app.on_event("startup")
async def startup_event():
    """Startup tasks"""
    logger.info("🚀 Simple Voice Assistant API Server starting...")
    logger.info(f"📡 API available at: http://{config.api_host}:{config.api_port}")
    logger.info(f"📖 Documentation at: http://{config.api_host}:{config.api_port}/docs")
    logger.info(f"🔌 WebSocket at: ws://{config.api_host}:{config.api_port}/ws/voice")
    logger.info(f"🔑 API Key: {config.api_key}")
    
    # Auto-start WebSocket simulation
    await manager.start_websocket_server()

if __name__ == "__main__":
    uvicorn.run(
        "simple_api_server:app",
        host=config.api_host,
        port=config.api_port,
        reload=False,
        log_level="info"
    )
