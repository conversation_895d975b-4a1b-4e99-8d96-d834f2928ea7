"""
Startup script for the Voice Assistant API Server

This script provides an easy way to start the API server with proper configuration.
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'psutil',
        'pydantic-settings'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True


def setup_environment(args):
    """Setup environment variables"""
    env_vars = {
        'PYTHONPATH': str(Path.cwd()),
        'VOICE_API_API_HOST': args.host,
        'VOICE_API_API_PORT': str(args.port),
        'VOICE_API_WEBSOCKET_HOST': args.ws_host,
        'VOICE_API_WEBSOCKET_PORT': str(args.ws_port),
        'LOG_LEVEL': args.log_level.upper()
    }
    
    # Set API key if provided
    if args.api_key:
        env_vars['VOICE_API_API_KEY'] = args.api_key
    
    # Set CORS origins if provided
    if args.cors_origins:
        env_vars['VOICE_API_CORS_ORIGINS'] = json.dumps(args.cors_origins)
    
    # Update environment
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"🔧 {key}={value}")


def check_config_files():
    """Check if configuration files exist"""
    config_dir = Path("config")
    default_config = config_dir / "default.ini"
    
    if not config_dir.exists():
        print("📁 Creating config directory...")
        config_dir.mkdir(exist_ok=True)
    
    if not default_config.exists():
        print("⚠️  Warning: config/default.ini not found")
        print("   The API server will use built-in defaults")
    else:
        print("✅ Configuration files found")


def check_required_files():
    """Check if required files exist"""
    required_files = [
        "api_server.py",
        "websocket_voice_server.py",
        "src/config_manager.py",
        "api_config_manager.py",
        "security.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ All required files found")
    return True


def start_server(args):
    """Start the API server"""
    print("🚀 Starting Voice Assistant API Server...")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check required files
    if not check_required_files():
        sys.exit(1)
    
    # Check configuration
    check_config_files()
    
    # Setup environment
    print("\n🔧 Setting up environment...")
    setup_environment(args)
    
    # Start server
    print(f"\n🌐 Starting server on {args.host}:{args.port}")
    print(f"🔌 WebSocket server on {args.ws_host}:{args.ws_port}")
    print(f"📊 Log level: {args.log_level.upper()}")
    
    if args.reload:
        print("🔄 Auto-reload enabled (development mode)")
    
    print("\n" + "=" * 50)
    print("🎙️  Voice Assistant API Server Starting...")
    print("=" * 50)
    
    try:
        # Import and run the server
        import uvicorn
        uvicorn.run(
            "api_server:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level.lower(),
            access_log=args.access_log
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Voice Assistant API Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start with defaults
  python start_api_server.py
  
  # Start on specific host/port
  python start_api_server.py --host 0.0.0.0 --port 8080
  
  # Start with custom API key
  python start_api_server.py --api-key your-secure-key-here
  
  # Start in development mode with auto-reload
  python start_api_server.py --reload --log-level debug
  
  # Start with custom CORS origins
  python start_api_server.py --cors-origins "http://localhost:3000" "https://myapp.com"
        """
    )
    
    # Server configuration
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="API server host (default: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="API server port (default: 8000)"
    )
    parser.add_argument(
        "--ws-host",
        default="localhost",
        help="WebSocket server host (default: localhost)"
    )
    parser.add_argument(
        "--ws-port",
        type=int,
        default=8765,
        help="WebSocket server port (default: 8765)"
    )
    
    # Security configuration
    parser.add_argument(
        "--api-key",
        help="API key for authentication"
    )
    parser.add_argument(
        "--cors-origins",
        nargs="*",
        default=["*"],
        help="CORS allowed origins (default: ['*'])"
    )
    
    # Development options
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload (development mode)"
    )
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="Log level (default: info)"
    )
    parser.add_argument(
        "--access-log",
        action="store_true",
        help="Enable access logging"
    )
    
    # Parse arguments
    args = parser.parse_args()
    
    # Start server
    start_server(args)


if __name__ == "__main__":
    main()
