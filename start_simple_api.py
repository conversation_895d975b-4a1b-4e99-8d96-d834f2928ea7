"""
Simple API Startup Script

This script starts a minimal version of the Voice Assistant API
that works with basic dependencies only.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_minimal_dependencies():
    """Check if minimal dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Install with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True


def main():
    """Main function"""
    print("🎙️ Simple Voice Assistant API")
    print("=" * 40)
    
    # Check dependencies
    if not check_minimal_dependencies():
        print("\n💡 Quick install:")
        print("   pip install fastapi uvicorn")
        sys.exit(1)
    
    # Set environment variables
    os.environ['PYTHONPATH'] = str(Path.cwd())
    
    # Set default API key if not provided
    if 'VOICE_API_API_KEY' not in os.environ:
        import secrets
        api_key = secrets.token_urlsafe(32)
        os.environ['VOICE_API_API_KEY'] = api_key
    
    print("✅ Dependencies OK")
    print("🚀 Starting simple API server...")
    print("\nFeatures available:")
    print("   ✅ REST API endpoints")
    print("   ✅ WebSocket connections")
    print("   ✅ Health monitoring")
    print("   ✅ Basic authentication")
    print("\nLimitations:")
    print("   ❌ No real audio processing")
    print("   ❌ No advanced configuration")
    print("   ❌ Simulated responses only")
    
    print("\n" + "=" * 40)
    
    try:
        # Import and run the simple server
        import uvicorn
        uvicorn.run(
            "simple_api_server:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("\n💡 Try installing FastAPI:")
        print("   pip install fastapi uvicorn")


if __name__ == "__main__":
    main()
